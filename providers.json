[{"name": "Anthropic", "id": "anthropic", "api_key": "$ANTHROPIC_API_KEY", "api_endpoint": "$ANTHROPIC_API_ENDPOINT", "type": "anthropic", "default_large_model_id": "claude-sonnet-4-20250514", "default_small_model_id": "claude-3-5-haiku-20241022", "models": [{"id": "claude-opus-4-20250514", "name": "<PERSON> 4", "cost_per_1m_in": 15, "cost_per_1m_out": 75, "cost_per_1m_in_cached": 18.75, "cost_per_1m_out_cached": 1.5, "context_window": 200000, "default_max_tokens": 32000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "claude-sonnet-4-20250514", "name": "<PERSON> 4", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "claude-3-7-sonnet-20250219", "name": "Claude 3.7 Sonnet", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "claude-3-5-haiku-20241022", "name": "Claude 3.5 Haiku", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 1, "cost_per_1m_out_cached": 0.08, "context_window": 200000, "default_max_tokens": 5000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet (Old)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 5000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "claude-3-5-sonnet-20241022", "name": "Claude 3.5 Sonnet (New)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 5000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}]}, {"name": "OpenAI", "id": "openai", "api_key": "$OPENAI_API_KEY", "api_endpoint": "$OPENAI_API_ENDPOINT", "type": "openai", "default_large_model_id": "o4-mini", "default_small_model_id": "gpt-4o", "models": [{"id": "o4-mini", "name": "o4 Mini", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.275, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "low", "supports_attachments": true}, {"id": "o3", "name": "o3", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.5, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": true}, {"id": "gpt-4.1", "name": "GPT-4.1", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.5, "context_window": 1047576, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4.1-mini", "name": "GPT-4.1 Mini", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 1.5999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.09999999999999999, "context_window": 1047576, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4.1-nano", "name": "GPT-4.1 Nano", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.024999999999999998, "context_window": 1047576, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "o3-mini", "name": "o3 Mini", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.55, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": false}, {"id": "gpt-4o", "name": "GPT-4o", "cost_per_1m_in": 2.5, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 1.25, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4o-mini", "name": "GPT-4o-mini", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.075, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}]}, {"name": "Google Gemini", "id": "gemini", "api_key": "$GEMINI_API_KEY", "api_endpoint": "$GEMINI_API_ENDPOINT", "type": "gemini", "default_large_model_id": "gemini-2.5-pro", "default_small_model_id": "gemini-2.5-flash", "models": [{"id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro", "cost_per_1m_in": 1.25, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 1.625, "cost_per_1m_out_cached": 0.31, "context_window": 1048576, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gemini-2.5-flash", "name": "Gemini 2.5 Flash", "cost_per_1m_in": 0.3, "cost_per_1m_out": 2.5, "cost_per_1m_in_cached": 0.3833, "cost_per_1m_out_cached": 0.075, "context_window": 1048576, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}]}, {"name": "Azure OpenAI", "id": "azure", "api_key": "$AZURE_OPENAI_API_KEY", "api_endpoint": "$AZURE_OPENAI_API_ENDPOINT", "type": "azure", "default_large_model_id": "o4-mini", "default_small_model_id": "gpt-4o", "models": [{"id": "codex-mini-latest", "name": "Codex Mini", "cost_per_1m_in": 1.5, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.375, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": true}, {"id": "o4-mini", "name": "o4 Mini", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.275, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": true}, {"id": "o3", "name": "o3", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.5, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": true}, {"id": "o3-pro", "name": "o3 Pro", "cost_per_1m_in": 20, "cost_per_1m_out": 80, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": true}, {"id": "gpt-4.1", "name": "GPT-4.1", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.5, "context_window": 1047576, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4.1-mini", "name": "GPT-4.1 Mini", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 1.5999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.09999999999999999, "context_window": 1047576, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4.1-nano", "name": "GPT-4.1 Nano", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.024999999999999998, "context_window": 1047576, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4.5-preview", "name": "GPT-4.5 (Preview)", "cost_per_1m_in": 75, "cost_per_1m_out": 150, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 37.5, "context_window": 128000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "o3-mini", "name": "o3 Mini", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.55, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "default_reasoning_effort": "medium", "supports_attachments": false}, {"id": "gpt-4o", "name": "GPT-4o", "cost_per_1m_in": 2.5, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 1.25, "context_window": 128000, "default_max_tokens": 20000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gpt-4o-mini", "name": "GPT-4o-mini", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.075, "context_window": 128000, "default_max_tokens": 20000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}]}, {"name": "AWS Bedrock", "id": "bedrock", "type": "bedrock", "default_large_model_id": "anthropic.claude-sonnet-4-20250514-v1:0", "default_small_model_id": "anthropic.claude-3-5-haiku-20241022-v1:0", "models": [{"id": "anthropic.claude-opus-4-20250514-v1:0", "name": "AWS Claude Opus 4", "cost_per_1m_in": 15, "cost_per_1m_out": 75, "cost_per_1m_in_cached": 18.75, "cost_per_1m_out_cached": 1.5, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic.claude-sonnet-4-20250514-v1:0", "name": "AWS Claude Sonnet 4", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic.claude-3-7-sonnet-20250219-v1:0", "name": "AWS Claude 3.7 Sonnet", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic.claude-3-5-haiku-20241022-v1:0", "name": "AWS Claude 3.5 Haiku", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 1, "cost_per_1m_out_cached": 0.08, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}]}, {"name": "Google Vertex AI", "id": "vertexai", "type": "vertexai", "default_large_model_id": "gemini-2.5-pro", "default_small_model_id": "gemini-2.5-flash", "models": [{"id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro", "cost_per_1m_in": 1.25, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 1.625, "cost_per_1m_out_cached": 0.31, "context_window": 1048576, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "gemini-2.5-flash", "name": "Gemini 2.5 Flash", "cost_per_1m_in": 0.3, "cost_per_1m_out": 2.5, "cost_per_1m_in_cached": 0.3833, "cost_per_1m_out_cached": 0.075, "context_window": 1048576, "default_max_tokens": 50000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}]}, {"name": "xAI", "id": "xai", "api_key": "$XAI_API_KEY", "api_endpoint": "https://api.x.ai/v1", "type": "openai", "default_large_model_id": "grok-3", "default_small_model_id": "grok-3-mini", "models": [{"id": "grok-4", "name": "Grok 4", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.75, "context_window": 256000, "default_max_tokens": 20000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "grok-3-mini", "name": "Grok 3 Mini", "cost_per_1m_in": 0.3, "cost_per_1m_out": 0.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.075, "context_window": 131072, "default_max_tokens": 20000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "grok-3", "name": "Grok 3", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.75, "context_window": 131072, "default_max_tokens": 20000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}]}, {"name": "Groq", "id": "groq", "api_key": "$GROQ_API_KEY", "api_endpoint": "https://api.groq.com/openai/v1", "type": "openai", "default_large_model_id": "moonshotai/kimi-k2-instruct", "default_small_model_id": "moonshotai/kimi-k2-instruct", "models": [{"id": "moonshotai/kimi-k2-instruct", "name": "Kimi K2", "cost_per_1m_in": 1, "cost_per_1m_out": 3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 10000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}]}, {"name": "OpenRouter", "id": "openrouter", "api_key": "$OPENROUTER_API_KEY", "api_endpoint": "https://openrouter.ai/api/v1", "type": "openai", "default_large_model_id": "anthropic/claude-sonnet-4", "default_small_model_id": "anthropic/claude-3.5-haiku", "models": [{"id": "openrouter/horizon-alpha", "name": "Horizon Alpha", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 256000, "default_max_tokens": 64000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "z-ai/glm-4.5", "name": "Z.AI: GLM 4.5", "cost_per_1m_in": 0.6, "cost_per_1m_out": 2.2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 65536, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "z-ai/glm-4.5-air", "name": "Z.AI: GLM 4.5 Air", "cost_per_1m_in": 0.19999999999999998, "cost_per_1m_out": 1.1, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.03, "context_window": 128000, "default_max_tokens": 48000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-235b-a22b-thinking-2507", "name": "Qwen: Qwen3 235B A22B Thinking 2507", "cost_per_1m_in": 0.1179, "cost_per_1m_out": 0.1179, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 262144, "default_max_tokens": 26214, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "z-ai/glm-4-32b", "name": "Z.AI: GLM 4 32B ", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.09999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 12800, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-coder:free", "name": "Qwen: Qwen3 Coder  (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 262144, "default_max_tokens": 26214, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-coder", "name": "Qwen: Qwen3 Coder ", "cost_per_1m_in": 1, "cost_per_1m_out": 5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.39999999999999997, "context_window": 1000000, "default_max_tokens": 32768, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-2.5-flash-lite", "name": "Google: Gemini 2.5 Flash Lite", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0.18330000000000002, "cost_per_1m_out_cached": 0.024999999999999998, "context_window": 1048576, "default_max_tokens": 32767, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "qwen/qwen3-235b-a22b-2507", "name": "Qwen: Qwen3 235B A22B Instruct 2507", "cost_per_1m_in": 0.22, "cost_per_1m_out": 0.88, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 262144, "default_max_tokens": 26214, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "moonshotai/kimi-k2:free", "name": "MoonshotAI: <PERSON><PERSON> (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "moonshotai/kimi-k2", "name": "MoonshotAI: <PERSON><PERSON>2", "cost_per_1m_in": 0.6, "cost_per_1m_out": 2.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/devstral-medium", "name": "Mistral: Devstral Medium", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/devstral-small", "name": "Mistral: Devstral Small 1.1", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "x-ai/grok-4", "name": "xAI: Grok 4", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.75, "context_window": 256000, "default_max_tokens": 25600, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/mistral-small-3.2-24b-instruct:free", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3.2 24B (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 96000, "default_max_tokens": 9600, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/mistral-small-3.2-24b-instruct", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3.2 24B", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "minimax/minimax-m1", "name": "MiniMax: MiniMax M1", "cost_per_1m_in": 0.55, "cost_per_1m_out": 2.2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 1000000, "default_max_tokens": 20000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-2.5-flash-lite-preview-06-17", "name": "Google: Gemini 2.5 Flash Lite Preview 06-17", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0.18330000000000002, "cost_per_1m_out_cached": 0.024999999999999998, "context_window": 1048576, "default_max_tokens": 32767, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "google/gemini-2.5-flash", "name": "Google: Gemini 2.5 Flash", "cost_per_1m_in": 0.3, "cost_per_1m_out": 2.5, "cost_per_1m_in_cached": 0.3833, "cost_per_1m_out_cached": 0.075, "context_window": 1048576, "default_max_tokens": 32767, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "google/gemini-2.5-pro", "name": "Google: Gemini 2.5 Pro", "cost_per_1m_in": 1.25, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 1.625, "cost_per_1m_out_cached": 0.31, "context_window": 1048576, "default_max_tokens": 32768, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/o3-pro", "name": "OpenAI: o3 Pro", "cost_per_1m_in": 20, "cost_per_1m_out": 80, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "x-ai/grok-3-mini", "name": "xAI: Grok 3 Mini", "cost_per_1m_in": 0.6, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.15, "context_window": 131072, "default_max_tokens": 13107, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "x-ai/grok-3", "name": "xAI: Grok 3", "cost_per_1m_in": 5, "cost_per_1m_out": 25, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 1.25, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/magistral-small-2506", "name": "Mistral: Magistral Small 2506", "cost_per_1m_in": 0.5, "cost_per_1m_out": 1.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 40000, "default_max_tokens": 20000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/magistral-medium-2506", "name": "Mistral: Magistral Medium 2506", "cost_per_1m_in": 2, "cost_per_1m_out": 5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 40960, "default_max_tokens": 20000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/magistral-medium-2506:thinking", "name": "Mistral: Magistral Medium 2506 (thinking)", "cost_per_1m_in": 2, "cost_per_1m_out": 5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 40960, "default_max_tokens": 20000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-2.5-pro-preview", "name": "Google: Gemini 2.5 Pro Preview 06-05", "cost_per_1m_in": 1.25, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 1.625, "cost_per_1m_out_cached": 0.31, "context_window": 1048576, "default_max_tokens": 32768, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "deepseek/deepseek-r1-0528", "name": "DeepSeek: R1 0528", "cost_per_1m_in": 2.5500000000000003, "cost_per_1m_out": 5.95, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 163840, "default_max_tokens": 65536, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "anthropic/claude-opus-4", "name": "Anthropic: <PERSON> 4", "cost_per_1m_in": 15, "cost_per_1m_out": 75, "cost_per_1m_in_cached": 18.75, "cost_per_1m_out_cached": 1.5, "context_window": 200000, "default_max_tokens": 16000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-sonnet-4", "name": "Anthropic: <PERSON> 4", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 32000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/devstral-small-2505:free", "name": "Mistral: Devstral Small 2505 (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/devstral-small-2505", "name": "Mistral: Devstral Small 2505", "cost_per_1m_in": 0.03, "cost_per_1m_out": 0.03, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/codex-mini", "name": "OpenAI: Codex Mini", "cost_per_1m_in": 1.5, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.375, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/mistral-medium-3", "name": "Mistral: Mistral Medium 3", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "google/gemini-2.5-pro-preview-05-06", "name": "Google: Gemini 2.5 Pro Preview 05-06", "cost_per_1m_in": 1.25, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 1.625, "cost_per_1m_out_cached": 0.31, "context_window": 1048576, "default_max_tokens": 32768, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "arcee-ai/virtuoso-large", "name": "Arcee AI: Virtuoso <PERSON>", "cost_per_1m_in": 0.75, "cost_per_1m_out": 1.2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 32000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-4b:free", "name": "Qwen: <PERSON>wen3 4B (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 40960, "default_max_tokens": 4096, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-30b-a3b", "name": "Qwen: Qwen3 30B A3B", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 4000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-14b", "name": "Qwen: <PERSON>wen3 14B", "cost_per_1m_in": 0.06, "cost_per_1m_out": 0.24, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 40960, "default_max_tokens": 20480, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-32b", "name": "Qwen: <PERSON>wen3 32B", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 4000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-235b-a22b:free", "name": "Qwen: Qwen3 235B A22B (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen3-235b-a22b", "name": "Qwen: Qwen3 235B A22B", "cost_per_1m_in": 0.19999999999999998, "cost_per_1m_out": 0.7999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 4000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/o4-mini-high", "name": "OpenAI: o4 Mini High", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.275, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/o3", "name": "OpenAI: o3", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.5, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/o4-mini", "name": "OpenAI: o4 Mini", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.275, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4.1", "name": "OpenAI: GPT-4.1", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.5, "context_window": 1047576, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4.1-mini", "name": "OpenAI: GPT-4.1 Mini", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 1.5999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.09999999999999999, "context_window": 1047576, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4.1-nano", "name": "OpenAI: GPT-4.1 Nano", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.024999999999999998, "context_window": 1047576, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "x-ai/grok-3-mini-beta", "name": "xAI: Grok 3 Mini Beta", "cost_per_1m_in": 0.6, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.15, "context_window": 131072, "default_max_tokens": 13107, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "x-ai/grok-3-beta", "name": "xAI: Grok 3 Beta", "cost_per_1m_in": 5, "cost_per_1m_out": 25, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 1.25, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "meta-llama/llama-4-maverick", "name": "Meta: Llama 4 Maverick", "cost_per_1m_in": 0.22, "cost_per_1m_out": 0.88, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 1048576, "default_max_tokens": 104857, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "meta-llama/llama-4-scout", "name": "Meta: Llama 4 Scout", "cost_per_1m_in": 0.08, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 1048576, "default_max_tokens": 524288, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "google/gemini-2.5-pro-exp-03-25", "name": "Google: Gemini 2.5 Pro Experimental", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 1048576, "default_max_tokens": 32767, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "deepseek/deepseek-chat-v3-0324:free", "name": "DeepSeek: DeepSeek V3 0324 (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 163840, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "deepseek/deepseek-chat-v3-0324", "name": "DeepSeek: DeepSeek V3 0324", "cost_per_1m_in": 0.28, "cost_per_1m_out": 1.1400000000000001, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 163840, "default_max_tokens": 81920, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-small-3.1-24b-instruct:free", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3.1 24B (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 96000, "default_max_tokens": 48000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "ai21/jamba-1.6-large", "name": "AI21: Jamba 1.6 Large", "cost_per_1m_in": 2, "cost_per_1m_out": 8, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 256000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "ai21/jamba-1.6-mini", "name": "AI21: Jamba Mini 1.6", "cost_per_1m_in": 0.19999999999999998, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 256000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-2.0-flash-lite-001", "name": "Google: Gemini 2.0 Flash Lite", "cost_per_1m_in": 0.075, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 1048576, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.7-sonnet", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON>", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 32000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.7-sonnet:thinking", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (thinking)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 64000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.7-sonnet:beta", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (self-moderated)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 64000, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/mistral-saba", "name": "Mistral: Sa<PERSON>", "cost_per_1m_in": 0.19999999999999998, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/o3-mini-high", "name": "OpenAI: o3 Mini High", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.55, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-2.0-flash-001", "name": "Google: Gemini 2.0 Flash", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0.18330000000000002, "cost_per_1m_out_cached": 0.024999999999999998, "context_window": 1048576, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "qwen/qwen-turbo", "name": "Qwen: <PERSON><PERSON>-<PERSON>", "cost_per_1m_in": 0.049999999999999996, "cost_per_1m_out": 0.19999999999999998, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.02, "context_window": 1000000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen-plus", "name": "<PERSON>wen: <PERSON><PERSON>-Plus", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 1.2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.16, "context_window": 131072, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen-max", "name": "<PERSON><PERSON>: <PERSON><PERSON>-<PERSON> ", "cost_per_1m_in": 1.5999999999999999, "cost_per_1m_out": 6.3999999999999995, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.64, "context_window": 32768, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/o3-mini", "name": "OpenAI: o3 Mini", "cost_per_1m_in": 1.1, "cost_per_1m_out": 4.4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.55, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-small-24b-instruct-2501", "name": "Mistral: Mistral Small 3", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "deepseek/deepseek-r1", "name": "DeepSeek: R1", "cost_per_1m_in": 0.44999999999999996, "cost_per_1m_out": 2.1500000000000004, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 163840, "default_max_tokens": 81920, "can_reason": true, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/codestral-2501", "name": "Mistral: Codestral 2501", "cost_per_1m_in": 0.3, "cost_per_1m_out": 0.8999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 262144, "default_max_tokens": 26214, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "deepseek/deepseek-chat", "name": "DeepSeek: DeepSeek V3", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 1.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 64000, "default_max_tokens": 8000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/o1", "name": "OpenAI: o1", "cost_per_1m_in": 15, "cost_per_1m_out": 60, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 7.5, "context_window": 200000, "default_max_tokens": 50000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "x-ai/grok-2-1212", "name": "xAI: Grok 2 1212", "cost_per_1m_in": 2, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-2.0-flash-exp:free", "name": "Google: Gemini 2.0 Flash Experimental (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 1048576, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "meta-llama/llama-3.3-70b-instruct:free", "name": "Meta: Llama 3.3 70B Instruct (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 65536, "default_max_tokens": 6553, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "meta-llama/llama-3.3-70b-instruct", "name": "Meta: Llama 3.3 70B Instruct", "cost_per_1m_in": 0.038000000000000006, "cost_per_1m_out": 0.12, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "amazon/nova-lite-v1", "name": "Amazon: Nova Lite 1.0", "cost_per_1m_in": 0.06, "cost_per_1m_out": 0.24, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 300000, "default_max_tokens": 2560, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "amazon/nova-micro-v1", "name": "Amazon: Nova Micro 1.0", "cost_per_1m_in": 0.035, "cost_per_1m_out": 0.14, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2560, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "amazon/nova-pro-v1", "name": "Amazon: Nova Pro 1.0", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 3.1999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 300000, "default_max_tokens": 2560, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4o-2024-11-20", "name": "OpenAI: GPT-4o (2024-11-20)", "cost_per_1m_in": 2.5, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 1.25, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/mistral-large-2411", "name": "Mistral Large 2411", "cost_per_1m_in": 2, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-large-2407", "name": "Mistral Large 2407", "cost_per_1m_in": 2, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/pixtral-large-2411", "name": "Mistral: Pixtral Large 2411", "cost_per_1m_in": 2, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "thedrummer/unslopnemo-12b", "name": "TheDrummer: UnslopNemo 12B", "cost_per_1m_in": 0.39999999999999997, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32000, "default_max_tokens": 16000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "anthropic/claude-3.5-haiku:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 1, "cost_per_1m_out_cached": 0.08, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.5-haiku", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 1, "cost_per_1m_out_cached": 0.08, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.5-haiku-20241022", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-10-22)", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 1, "cost_per_1m_out_cached": 0.08, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.5-sonnet:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.5-sonnet", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/ministral-8b", "name": "Mistral: Ministral 8B", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.09999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 12800, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/ministral-3b", "name": "Mistral: Ministral 3B", "cost_per_1m_in": 0.04, "cost_per_1m_out": 0.04, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "nvidia/llama-3.1-nemotron-70b-instruct", "name": "NVIDIA: Llama 3.1 Nemotron 70B Instruct", "cost_per_1m_in": 0.12, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 65536, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-flash-1.5-8b", "name": "Google: Gemini 1.5 Flash 8B", "cost_per_1m_in": 0.0375, "cost_per_1m_out": 0.15, "cost_per_1m_in_cached": 0.0583, "cost_per_1m_out_cached": 0.01, "context_window": 1000000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "thedrummer/rocinante-12b", "name": "TheDrummer: Rocinante 12B", "cost_per_1m_in": 0.24, "cost_per_1m_out": 0.44999999999999996, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 16384, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "meta-llama/llama-3.2-3b-instruct", "name": "Meta: Llama 3.2 3B Instruct", "cost_per_1m_in": 0.015, "cost_per_1m_out": 0.024999999999999998, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 65536, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "qwen/qwen-2.5-72b-instruct", "name": "Qwen2.5 72B Instruct", "cost_per_1m_in": 0.12, "cost_per_1m_out": 0.39, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "cohere/command-r-plus-08-2024", "name": "Cohere: Command R+ (08-2024)", "cost_per_1m_in": 2.5, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "cohere/command-r-08-2024", "name": "Cohere: Command R (08-2024)", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "microsoft/phi-3.5-mini-128k-instruct", "name": "Microsoft: Phi-3.5 Mini 128K Instruct", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.09999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 12800, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-4o-2024-08-06", "name": "OpenAI: GPT-4o (2024-08-06)", "cost_per_1m_in": 2.5, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 1.25, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "meta-llama/llama-3.1-8b-instruct", "name": "Meta: Llama 3.1 8B Instruct", "cost_per_1m_in": 0.015, "cost_per_1m_out": 0.02, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "meta-llama/llama-3.1-405b-instruct", "name": "Meta: Llama 3.1 405B Instruct", "cost_per_1m_in": 0.7999999999999999, "cost_per_1m_out": 0.7999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 65536, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "meta-llama/llama-3.1-70b-instruct", "name": "Meta: Llama 3.1 70B Instruct", "cost_per_1m_in": 0.88, "cost_per_1m_out": 0.88, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-nemo", "name": "Mistral: <PERSON><PERSON><PERSON> Nemo", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 131072, "default_max_tokens": 13107, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-4o-mini", "name": "OpenAI: GPT-4o-mini", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.075, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4o-mini-2024-07-18", "name": "OpenAI: GPT-4o-mini (2024-07-18)", "cost_per_1m_in": 0.15, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0.075, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.5-sonnet-20240620:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON> (2024-06-20) (self-moderated)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3.5-sonnet-20240620", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-06-20)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "mistralai/mistral-7b-instruct:free", "name": "Mistral: Mistral 7B Instruct (free)", "cost_per_1m_in": 0, "cost_per_1m_out": 0, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistral<PERSON>/mistral-7b-instruct", "name": "Mistral: Mistral 7B Instruct", "cost_per_1m_in": 0.028, "cost_per_1m_out": 0.054, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-7b-instruct-v0.3", "name": "Mistral: Mistral 7B Instruct v0.3", "cost_per_1m_in": 0.028, "cost_per_1m_out": 0.054, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "microsoft/phi-3-mini-128k-instruct", "name": "Microsoft: Phi-3 Mini 128K Instruct", "cost_per_1m_in": 0.09999999999999999, "cost_per_1m_out": 0.09999999999999999, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 12800, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "microsoft/phi-3-medium-128k-instruct", "name": "Microsoft: Phi-3 Medium 128K Instruct", "cost_per_1m_in": 1, "cost_per_1m_out": 1, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 12800, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-flash-1.5", "name": "Google: Gemini 1.5 Flash ", "cost_per_1m_in": 0.075, "cost_per_1m_out": 0.3, "cost_per_1m_in_cached": 0.1583, "cost_per_1m_out_cached": 0.01875, "context_window": 1000000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4o", "name": "OpenAI: GPT-4o", "cost_per_1m_in": 2.5, "cost_per_1m_out": 10, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4o:extended", "name": "OpenAI: GPT-4o (extended)", "cost_per_1m_in": 6, "cost_per_1m_out": 18, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 32000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4o-2024-05-13", "name": "OpenAI: GPT-4o (2024-05-13)", "cost_per_1m_in": 5, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "meta-llama/llama-3-8b-instruct", "name": "Meta: Llama 3 8B Instruct", "cost_per_1m_in": 0.03, "cost_per_1m_out": 0.06, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 8192, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "meta-llama/llama-3-70b-instruct", "name": "Meta: Llama 3 70B Instruct", "cost_per_1m_in": 0.3, "cost_per_1m_out": 0.39999999999999997, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 8192, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mixtral-8x22b-instruct", "name": "Mistral: Mixtral 8x22B Instruct", "cost_per_1m_in": 2, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 65536, "default_max_tokens": 6553, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "google/gemini-pro-1.5", "name": "Google: Gemini 1.5 Pro", "cost_per_1m_in": 1.25, "cost_per_1m_out": 5, "cost_per_1m_in_cached": 2.875, "cost_per_1m_out_cached": 0.625, "context_window": 2000000, "default_max_tokens": 4096, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "openai/gpt-4-turbo", "name": "OpenAI: GPT-4 Turbo", "cost_per_1m_in": 10, "cost_per_1m_out": 30, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "cohere/command-r-plus", "name": "Cohere: Command R+", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "cohere/command-r-plus-04-2024", "name": "Cohere: Command R+ (04-2024)", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "cohere/command-r", "name": "Cohere: Command R", "cost_per_1m_in": 0.5, "cost_per_1m_out": 1.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "anthropic/claude-3-haiku:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "cost_per_1m_in": 0.25, "cost_per_1m_out": 1.25, "cost_per_1m_in_cached": 0.3, "cost_per_1m_out_cached": 0.03, "context_window": 200000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3-haiku", "name": "Anthropic: <PERSON> 3 <PERSON><PERSON>", "cost_per_1m_in": 0.25, "cost_per_1m_out": 1.25, "cost_per_1m_in_cached": 0.3, "cost_per_1m_out_cached": 0.03, "context_window": 200000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3-opus:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "cost_per_1m_in": 15, "cost_per_1m_out": 75, "cost_per_1m_in_cached": 18.75, "cost_per_1m_out_cached": 1.5, "context_window": 200000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3-opus", "name": "Anthropic: <PERSON> 3 Opus", "cost_per_1m_in": 15, "cost_per_1m_out": 75, "cost_per_1m_in_cached": 18.75, "cost_per_1m_out_cached": 1.5, "context_window": 200000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "anthropic/claude-3-sonnet", "name": "Anthropic: <PERSON> 3 <PERSON>", "cost_per_1m_in": 3, "cost_per_1m_out": 15, "cost_per_1m_in_cached": 3.75, "cost_per_1m_out_cached": 0.3, "context_window": 200000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": true}, {"id": "cohere/command-r-03-2024", "name": "Cohere: Command R (03-2024)", "cost_per_1m_in": 0.5, "cost_per_1m_out": 1.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2000, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-large", "name": "Mistral Large", "cost_per_1m_in": 2, "cost_per_1m_out": 6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 12800, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-3.5-turbo-0613", "name": "OpenAI: GPT-3.5 Turbo (older v0613)", "cost_per_1m_in": 1, "cost_per_1m_out": 2, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 4095, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-4-turbo-preview", "name": "OpenAI: GPT-4 Turbo Preview", "cost_per_1m_in": 10, "cost_per_1m_out": 30, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-small", "name": "Mistra<PERSON> Small", "cost_per_1m_in": 0.19999999999999998, "cost_per_1m_out": 0.6, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mistral-tiny", "name": "Mistral Tiny", "cost_per_1m_in": 0.25, "cost_per_1m_out": 0.25, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 3276, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "mistralai/mixtral-8x7b-instruct", "name": "Mistral: Mixtral 8x7B Instruct", "cost_per_1m_in": 0.08, "cost_per_1m_out": 0.24, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 32768, "default_max_tokens": 8192, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-4-1106-preview", "name": "OpenAI: GPT-4 Turbo (older v1106)", "cost_per_1m_in": 10, "cost_per_1m_out": 30, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 128000, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-3.5-turbo-16k", "name": "OpenAI: GPT-3.5 Turbo 16k", "cost_per_1m_in": 3, "cost_per_1m_out": 4, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 16385, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-3.5-turbo", "name": "OpenAI: GPT-3.5 Turbo", "cost_per_1m_in": 0.5, "cost_per_1m_out": 1.5, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 16385, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-4", "name": "OpenAI: GPT-4", "cost_per_1m_in": 30, "cost_per_1m_out": 60, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 8191, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}, {"id": "openai/gpt-4-0314", "name": "OpenAI: GPT-4 (older v0314)", "cost_per_1m_in": 30, "cost_per_1m_out": 60, "cost_per_1m_in_cached": 0, "cost_per_1m_out_cached": 0, "context_window": 8191, "default_max_tokens": 2048, "can_reason": false, "has_reasoning_efforts": false, "supports_attachments": false}], "default_headers": {"HTTP-Referer": "https://charm.land", "X-Title": "Crush"}}]